// Import the global sidebar manager
import SidebarManager from '../shared/sidebar_manager';

// Notification handling
class NotificationManager {
  constructor() {
    this.toastContainer = document.getElementById('notification-toast-container');
    this.notificationSidebar = document.getElementById('notification-sidebar');
    this.overlay = document.getElementById('shared-sidebar-overlay');
    this.closeNotificationsBtn = document.getElementById('close-notifications');
    this.remindInDropdown = document.getElementById('remind-in-dropdown-container');
    this.currentNotificationId = null;
    this.currentNotificationIndex = null;

    this.initializeEventListeners();
    this.initializeTabs();
    this.initializePusher();
    this.initializeReminderChecker();
    
    // Register with the global sidebar manager
    if (this.notificationSidebar) {
      SidebarManager.register('notification-sidebar', 
        () => this.openSidebarInternal(),
        () => this.closeSidebarInternal()
      );
    }
  }

  initializeEventListeners() {
    // Sidebar toggle
    this.closeNotificationsBtn?.addEventListener('click', () => this.closeSidebar());
    this.overlay?.addEventListener('click', () => this.closeSidebar());

    // Initialize notification toggle if it exists
    const notificationToggle = document.getElementById('notification-toggle');
    notificationToggle?.addEventListener('click', (e) => this.toggleSidebar(e));

    // Set reminder button
    const setReminderBtn = document.getElementById('set-reminder');
    setReminderBtn?.addEventListener('click', () => this.handleSetReminder());

    // Handle action buttons (including remind-in buttons)
    document.addEventListener('click', (e) => {
      const actionButton = e.target.closest('.action-button-split');
      if (!actionButton) return;
      
      const notificationId = actionButton.dataset.notificationId;
      const action = actionButton.dataset.action;
      const index = actionButton.dataset.index;
      
      if (action === 'remind_in') {
        // Show SweetAlert for remind-in options
        this.showRemindInSweetAlert(notificationId, index);
      } else {
        // Handle other actions normally
        this.handleAction(notificationId, action, index);
      }
    });

    document.querySelector('.notification-content').addEventListener("pusher:after-prepend", (e) => {
      $(".notification-sidebar .empty-state").remove();

      // Handle regular action buttons in newly added notifications
      Array.from(e.target.querySelectorAll('.action-button-split:not(.remind-in-toggle)')).forEach(button => {
        button.addEventListener('click', (e) => {
          const notificationId = e.target.dataset.notificationId;
          const action = e.target.dataset.action;
          const index = e.target.dataset.index;

          this.handleAction(notificationId, action, index);
        });
      });
    });

    document.querySelector(".clear-all-button").addEventListener("click", () => {
      this.clearAll();
    });
  }

  initializeTabs() {
    const tabs = document.querySelectorAll('.tab');
    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        tabs.forEach(t => t.classList.remove('active'));
        tab.classList.add('active');
        this.filterNotifications();
      });
    });
  }

  initializePusher() {
    const [pusher, channel] = connectPusherActions(`private-notifications.${window._currentUserId}`);

    channel.bind("notification", data => {
      // Log the notification data to help with debugging
      console.log("Received notification via Pusher:", data);
      
      // Make sure we have all the required data
      if (data && data.id) {
        // Handle the notification (show toast)
        this.handleNotification(data);
        
        // Also refresh the notifications list in the sidebar
        this.refreshNotifications();
        
        // Update the notification count badge
        this.updateNotificationCount();
      } else {
        console.error("Received invalid notification data:", data);
      }
    });

    channel.bind("attended", () => {
      this.patientAttended();
    });
  }

  initializeReminderChecker() {
    // Check for scheduled reminders every minute
    setInterval(() => {
      this.checkScheduledReminders();
    }, 60000); // 60 seconds
    
    // Also check on page load
    this.checkScheduledReminders();
  }
  
  checkScheduledReminders() {
    $.ajax({
      url: '/admin/notifications/check_scheduled',
      type: 'GET',
      headers: {
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      },
      success: (response) => {
        if (response.new_notifications && response.new_notifications > 0) {
          // Refresh the notifications list
          this.refreshNotifications();
          
          // Show a notification about the reminders
          const message = response.new_notifications === 1 
            ? 'A reminder is now active' 
            : `${response.new_notifications} reminders are now active`;
          
          toastr.info(message, 'Reminder Alert', {
            timeOut: 5000,
            closeButton: true,
            progressBar: true
          });
        }
      }
    });
  }
  
  refreshNotifications() {
    // Reload the notifications in the sidebar
    $.ajax({
      url: '/admin/notifications',
      type: 'GET',
      dataType: 'html',
      headers: {
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      },
      success: (html) => {
        // Update the notification content
        const notificationContent = document.querySelector('.notification-content');
        if (notificationContent) {
          notificationContent.innerHTML = html;
        }
        
        // Update the notification count
        const count = document.querySelectorAll('.notification-card').length;
        const badge = document.querySelector('#notification-toggle .badge');
        if (badge) {
          badge.textContent = count;
          badge.style.display = count > 0 ? 'flex' : 'none';
        }
      }
    });
  }

  updateNotificationCount() {
    // Get the current count of unread notifications
    const count = document.querySelectorAll('.notification-card').length;
    const badge = document.querySelector('#notification-toggle .badge');
    if (badge) {
      badge.textContent = count;
      badge.style.display = count > 0 ? 'flex' : 'none';
    }
  }

  handleNotification(data) {
    console.log("Handling notification:", data);
    
    // Ensure we have valid data before showing the toast
    if (!data || !data.id) {
      console.error("Invalid notification data:", data);
      return;
    }
    
    // Transform actions for the toast
    const transformedActions = (data.actions || []).map((action, index) => 
      this.transformAction(data, action, index)
    );
    
    // Show the toast notification
    this.showToast({ ...data, actions: transformedActions });
  }

  transformAction(data, action, index) {
    return {
      ...action,
      callback: () => {
        this.handleAction(data.id, action.action, index);
      }
    }
  }

  handleAction(notificationId, action, index) {
    fetch(`/admin/notifications/${notificationId}/handle_action`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify({ action_name: action, index })
    }).then(response => {
      if (response.ok) {
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.indexOf("application/json") !== -1) {
          response.json().then(json => {
            if (json.redirect) window.location.href = json.redirect;
            if (json.action_type === 'remind_in') {
              this.showRemindInModal(json.action_id);
            }
          });
        }
        // Remove the notification card after successful action
        const card = document.querySelector(`[data-notification-id="${notificationId}"]`);
        card?.remove();
        this.checkVisibleNotifications();
      }
    });
  }

  handleRemindIn(notificationId, index, minutes) {
    $.ajax({
      url: `/admin/notifications/${notificationId}/handle_action`,
      type: 'POST',
      headers: {
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      },
      data: {
        action_name: 'remind_in',
        index: index,
        minutes: minutes
      },
      success: (response) => {
        // Remove the notification from the sidebar
        const notificationCard = document.querySelector(`.notification-card[data-notification-id="${notificationId}"]`);
        if (notificationCard) {
          notificationCard.remove();
        }
        
        // Show success message
        if (response.message) {
          Swal.fire({
            title: 'Reminder Set',
            text: response.message,
            icon: 'success',
            timer: 3000,
            showConfirmButton: false
          });
        }
      },
      error: (error) => {
        console.error('Error setting reminder:', error);
        Swal.fire({
          title: 'Error',
          text: 'Failed to set reminder. Please try again.',
          icon: 'error'
        });
      }
    });
  }

  showRemindInModal(actionId) {
    const modal = document.getElementById('remindInModal');
    const remindActionIdInput = document.getElementById('remind-action-id');
    remindActionIdInput.value = actionId;
    
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
  }

  showRemindInSweetAlert(notificationId, index) {
    Swal.fire({
      title: 'Remind in',
      html: `
        <div class="form-group">
          <select id="remind-time-select" class="form-select">
            <option value="">Select a time</option>
            <option value="5">5 minutes</option>
            <option value="15">15 minutes</option>
            <option value="30">30 minutes</option>
            <option value="60">1 hour</option>
            <option value="120">2 hours</option>
            <option value="240">4 hours</option>
            <option value="480">8 hours</option>
            <option value="1440">1 day</option>
          </select>
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: 'Set Reminder',
      cancelButtonText: 'Cancel',
      customClass: {
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn btn-secondary',
        popup: 'remind-in-popup'
      },
      buttonsStyling: false,
      preConfirm: () => {
        const selectValue = document.getElementById('remind-time-select').value;
        if (!selectValue) {
          Swal.showValidationMessage('Please select a time');
          return false;
        }
        return selectValue;
      }
    }).then((result) => {
      if (result.isConfirmed && result.value) {
        const minutes = result.value;
        this.handleRemindIn(notificationId, index, minutes);
      }
    });
  }

  toggleSidebar(e) {
    // Prevent any default behavior and stop event propagation
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    // Check if this sidebar is currently open
    const isOpen = SidebarManager.sidebars['notification-sidebar']?.isOpen;

    if (isOpen) {
      // If it's open, close it
      SidebarManager.close('notification-sidebar');
    } else {
      // If it's closed, open it
      SidebarManager.open('notification-sidebar');
    }
  }

  openSidebar() {
    // Use the global sidebar manager to handle opening
    SidebarManager.open('notification-sidebar');
  }
  
  // Internal function that actually performs the opening
  openSidebarInternal() {
    if (!this.notificationSidebar) {
      console.warn('Notification sidebar element not found');
      return;
    }
    // Use Tailwind transform classes for smooth animation
    this.notificationSidebar.classList.remove('translate-x-full');
    this.filterNotifications();
  }

  filterNotifications() {
    const tab = $(".notification-header .active").text().toLowerCase();
    $(".notification-card").show();
    if(tab === "today") {
      const date = new Date().toISOString().split("T")[0];
      $(`.notification-card:not([data-date="${date}"])`).hide();
    }

    this.checkVisibleNotifications();
  }

  checkVisibleNotifications() {
    const visibleNotifications = $(".notification-card:visible");
    if(visibleNotifications.length === 0) {
      $(".notification-sidebar .empty-state").show();
      $(".clear-all-container").hide();
    } else {
      $(".notification-sidebar .empty-state").hide();
      $(".clear-all-container").show();
    }
  }

  closeSidebar() {
    // Use the global sidebar manager to handle closing
    SidebarManager.close('notification-sidebar');
  }
  
  // Internal function that actually performs the closing
  closeSidebarInternal() {
    if (!this.notificationSidebar) {
      console.warn('Notification sidebar element not found');
      return;
    }
    // Use Tailwind transform classes for smooth animation
    this.notificationSidebar.classList.add('translate-x-full');
  }

  patientAttended() {
    var snd = new Audio("/audio/beep-1.mp3");
    snd.play();
  }

  showToast(data) {
    console.log("Showing toast for notification:", data);
    const toast = document.createElement('div');
    toast.className = 'toast-notification';
    toast.dataset.id = data.id;

    let iconSvg = '';
    switch (data.icon) {
      case 'message':
        iconSvg = '<i class="fa-thin fa-messages"></i>';
        break;
      case 'alert':
      case 'exclamation-triangle':
      case 'exclamation':
        iconSvg = '<i class="fa-thin fa-exclamation-triangle"></i>';
        break;
      case 'tasks':
        iconSvg = '<i class="fa-thin fa-tasks"></i>';
        break;
      case 'phone-rotary':
        iconSvg = '<i class="fa-thin fa-phone-rotary"></i>';
        break;
      case 'comment-exclamation':
        iconSvg = '<i class="fa-thin fa-comment-exclamation"></i>';
        break;
      case 'bell':
      default:
        iconSvg = '<i class="fa-thin fa-bell"></i>';
        break;
    }

    let badgeHtml = '';
    if (data.badge) {
      badgeHtml = `<span class="notification-badge badge-${data.badge}">${data.badge}</span>`;
    }

    let actionsHtml = '';
    if (data.actions && data.actions.length) {
      actionsHtml = `
        <div class="action-buttons">
          ${data.actions.map((action, index) => {
            if (action.action === 'remind_in') {
              return `
                <button class="action-button-split remind-in-button"
                        data-action="${action.action}"
                        data-notification-id="${data.id}"
                        data-index="${index}">
                  ${action.title}
                </button>
              `;
            } else {
              return `
                <button class="action-button-split ${action.primary ? 'primary' : ''}"
                        data-action="${action.action}"
                        data-notification-id="${data.id}"
                        data-index="${index}">
                  ${action.title}
                </button>
              `;
            }
          }).join('')}
        </div>
      `;
    }

    toast.innerHTML = `
      <div class="notification-content-area">
        <div class="notification-icon">
          ${iconSvg}
        </div>
        <div class="notification-details">
          <div class="notification-title">${data.title} ${badgeHtml}</div>
          <div class="notification-description">${data.description}</div>
        </div>
      </div>
      ${actionsHtml}
    `;

    // Add event listeners for action buttons
    setTimeout(() => {
      const actionButtons = toast.querySelectorAll('.action-button-split');
      actionButtons.forEach((button) => {
        button.addEventListener('click', () => {
          const notificationId = button.dataset.notificationId;
          const action = button.dataset.action;
          const actionIndex = button.dataset.index;
          
          this.handleAction(notificationId, action, actionIndex);
          this.removeToast(toast);
        });
      });
    }, 100);

    this.toastContainer.appendChild(toast);
    requestAnimationFrame(() => toast.classList.add('show'));

    // Auto-remove after 5 seconds
    if(!data.persistent) setTimeout(() => this.removeToast(toast), 5000);
    if(data.shakeEvery) setTimeout(() => this.shakeToast(toast, data.shakeEvery), data.shakeEvery);
    if(data.highlightAfter) setTimeout(() => this.highlightToast(toast, data.highlightAfter), data.highlightAfter);

    if(data.waitingRoom) {
      toast.querySelector(".notification-badge").textContent = "5m";
      toast.classList.add('wr-initial-toast');
      setTimeout(() => {
        // Appointment is due
        toast.classList.remove('wr-initial-toast');
        toast.classList.add('wr-appointment-due');
        var snd = new Audio("/audio/beep-2.mp3");
        snd.play();

        setTimeout(() => {
          // Appointment is late
          toast.classList.remove('wr-appointment-due');
          toast.classList.add('wr-appointment-late');
        }, 300000)
      }, 300000)
      let timeUntilAppt = 5,
          countdown = setInterval(() => {
            if(!document.body.contains(toast)) return clearInterval(countdown);
            timeUntilAppt--;
            if(timeUntilAppt > 0) {
              toast.querySelector(".notification-badge").textContent = `${timeUntilAppt}m`;
            } else if(timeUntilAppt === 0) {
                toast.querySelector(".notification-badge").textContent = "Now";
            } else if (timeUntilAppt === -5) {
                toast.querySelector(".notification-badge").textContent = "Late";
                clearInterval(countdown);
            }
        }, 60000);
    }
  }

  removeToast(toast) {
    toast.classList.add('hide');
    setTimeout(() => toast.parentNode?.removeChild(toast), 300);
  }

  shakeToast(toast, timeout) {
    if(!document.body.contains(toast)) return;
    const classes = ['animate__animated', 'animate__shakeX', 'animate__repeat-2']
    toast.classList.add(...classes);
    setTimeout(() => toast.classList.remove(...classes), 2000);
    setTimeout(() => this.shakeToast(toast, timeout), timeout);
  }

  highlightToast(toast) {
    toast.classList.add("toast-highlight");
  }

  clearAll() {
    const visibleNotifications = $(".notification-card:visible");
    fetch('/admin/notifications/clear_all', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify({ notification_ids: visibleNotifications.map((index, el) => el.dataset.notificationId).get() })
    }).then(response => {
        if (response.ok) {
            visibleNotifications.remove();
        }
    });
  }

  handleSetReminder() {
    const actionId = document.getElementById('remind-action-id').value;
    const reminderTime = document.getElementById('remind-time').value;
    
    fetch(`/admin/actions/${actionId}/set_reminder`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify({ minutes: reminderTime })
    }).then(response => {
      if (response.ok) {
        // Close the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('remindInModal'));
        modal.hide();
        
        // Show success toast using the existing toast system
        this.showToast({
          id: Date.now(),
          title: 'Reminder Set',
          description: `You will be reminded in ${document.getElementById('remind-time').options[document.getElementById('remind-time').selectedIndex].text}`,
          icon: 'bell'
        });
      }
    });
  }

  showRemindInDropdownAt(button) {
    // Position the dropdown near the button
    const buttonRect = button.getBoundingClientRect();
    this.remindInDropdown.style.top = `${buttonRect.bottom + window.scrollY}px`;
    this.remindInDropdown.style.left = `${buttonRect.left + window.scrollX}px`;
    this.remindInDropdown.style.width = `${buttonRect.width}px`;
    
    // Show the dropdown
    this.remindInDropdown.classList.add('show');
  }
  
  hideRemindInDropdown() {
    this.remindInDropdown.classList.remove('show');
    this.currentNotificationId = null;
    this.currentNotificationIndex = null;
  }
}

// Initialize notification manager when document is ready
document.addEventListener('DOMContentLoaded', () => {
  window.notificationManager = new NotificationManager();
});
