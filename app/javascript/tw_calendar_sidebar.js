// Import the global sidebar manager
import SidebarManager from './admin/shared/sidebar_manager';

// Global variables to track state across the application
var sidebarLoaded = false;
var sidebar = null;
var overlay = null;
var dentistsDropdown = null;
var dentistsButton = null;
var sidebarContainer = null;
var calendarButton = null;
var currentDate = null;

document.addEventListener('DOMContentLoaded', function() {
  setupCalendarSidebar();
  
  // Add a global click handler for date navigation buttons
  document.addEventListener('click', function(event) {
    const button = event.target.closest('[data-action="navigate-date"]');
    if (button) {
      event.preventDefault();
      handleDateNavigationClick({ target: button, preventDefault: () => {} });
    }
  });
  
  // Register calendar sidebar with the global manager
  SidebarManager.register('calendar-sidebar', 
    function() { openSidebarInternal(); },
    function() { closeSidebarInternal(); }
  );
});

function setupCalendarSidebar() {
  calendarButton = document.getElementById('calendar-button');
  sidebarContainer = document.getElementById('calendar-sidebar-container');

  if (!calendarButton || !sidebarContainer) {
    return;
  }
  
  // Setup appointment toggle functionality
  setupAppointmentToggle();

  // Remove any existing event listeners to prevent duplicates
  calendarButton.removeEventListener('click', handleCalendarButtonClick);
  calendarButton.addEventListener('click', handleCalendarButtonClick);
}

function handleCalendarButtonClick(event) {
  event.preventDefault();
  
  // Check if sidebar exists and is visible
  const sidebarExists = sidebar && document.body.contains(sidebar);
  const sidebarVisible = sidebarExists && sidebar.style.transform !== 'translateX(100%)';
  
  // Check if we need to reload the main calendar view
  const isInnerSidebar = sidebarExists && sidebar.querySelector('[aria-label="Go back"]') !== null;
  
  if (!sidebarExists || !sidebarLoaded || isInnerSidebar) {
    // Load the sidebar content via AJAX
    fetch('/admin/calendar_sidebar')
      .then(response => response.text())
      .then(html => {
        // Clear the container and add the new content
        sidebarContainer.innerHTML = '';
        sidebarContainer.insertAdjacentHTML('beforeend', html);
        
        // Get the sidebar element and ensure it's properly initialized
        sidebar = sidebarContainer.querySelector('[data-sidebar="sidebar"]')?.parentElement;
        if (!sidebar) {
          console.error('Failed to find sidebar element');
          return;
        }
        
        // Initialize the sidebar components
        setupSidebar();
        
        // Now that everything is set up, show the sidebar
        openSidebar();
        
        // Mark as loaded
        sidebarLoaded = true;
      })
      .catch(error => {
        console.error('Error loading calendar sidebar:', error);
      });
  } else if (sidebarVisible) {
    // If sidebar is visible, hide it
    closeSidebar();
  } else {
    // If sidebar exists but is hidden, show it
    openSidebar();
  }
}

function setupSidebar() {
  if (!sidebar) {
    return;
  }
  
  // Re-query the shared overlay element
  overlay = document.getElementById('shared-sidebar-overlay');
  
  // Setup dentists dropdown
  setupDentistsDropdown();
  
  // Setup date navigation buttons
  setupDateNavigationButtons();
  
  // Setup appointment detail buttons - this is critical for being able to open the inner sidebar
  setupAppointmentDetailButtons();
  
  // Setup new action button
  setupNewActionButton();
}

function createOverlay() {
  // Use the shared overlay instead of creating a new one
  overlay = document.getElementById('shared-sidebar-overlay');
  
  if (!overlay) {
    return;
  }
  
  // Setup overlay click to close
  overlay.addEventListener('click', closeSidebar);
}

function openSidebar() {
  // Use the global sidebar manager to handle opening
  SidebarManager.open('calendar-sidebar');
}

// Internal function that actually performs the opening
function openSidebarInternal() {
  if (!sidebar) {
    return;
  }

  sidebar.style.transform = 'translateX(0)';
}

function closeSidebar() {
  // Use the global sidebar manager to handle closing
  SidebarManager.close('calendar-sidebar');
}

// Internal function that actually performs the closing
function closeSidebarInternal() {
  if (!sidebar) {
    return;
  }

  sidebar.style.transform = 'translateX(100%)';

  // Close dentists dropdown if open
  if (dentistsDropdown) {
    closeDentistsDropdown();
  }

  // We don't set sidebarLoaded to false here because we want to keep the sidebar content
  // But we do need to check if we're in the inner sidebar view
  if (sidebar) {
    const isInnerSidebar = sidebar.querySelector('[aria-label="Go back"]') !== null;
    if (isInnerSidebar) {
      sidebarLoaded = false;
    }
  }
}

function setupDentistsDropdown() {
  if (!sidebar) {
    console.error('Sidebar not initialized');
    return;
  }
  
  // Find the dentists button
  dentistsButton = sidebar.querySelector('[data-dentists-button]');
  if (!dentistsButton) {
    console.error('Dentists button not found');
    return;
  }
  
  // Remove any existing event listeners to prevent duplicates
  const newButton = dentistsButton.cloneNode(true);
  dentistsButton.parentNode.replaceChild(newButton, dentistsButton);
  dentistsButton = newButton;
  
  // Set up click handler for the button
  dentistsButton.addEventListener('click', function(event) {
    event.preventDefault();
    event.stopPropagation();
    toggleDentistsDropdown();
  });
  
  // Close dropdown when clicking outside
  document.addEventListener('click', function handleClickOutside(event) {
    if (!dentistsDropdown || !dentistsButton) return;
    
    const isClickInside = dentistsButton.contains(event.target) || 
                        (dentistsDropdown && dentistsDropdown.contains(event.target));
    
    if (!isClickInside) {
      closeDentistsDropdown();
    }
  });
  
  // Initialize the dropdown content
  const dropdownId = dentistsButton.getAttribute('aria-controls');
  if (dropdownId) {
    dentistsDropdown = document.getElementById(dropdownId);
  }
}

function setupDateNavigationButtons() {
  const dateButtons = sidebar.querySelectorAll('[data-action="navigate-date"]');
  if (!dateButtons.length) {
    return;
  }
  
  dateButtons.forEach(button => {
    // Use a direct onclick handler to ensure it works
    button.onclick = function(event) {
      event.preventDefault();
      handleDateNavigationClick(event);
    };
  });
}

function handleDateNavigationClick(event) {
  event.preventDefault();
  // Use event.target or event.currentTarget depending on what's available
  const button = event.currentTarget || event.target;
  const dateString = button.getAttribute('data-date');
  
  if (!dateString) {
    return;
  }
  
  loadCalendarSidebarForDate(dateString);
}

function loadCalendarSidebarForDate(dateString) {
  // Keep the sidebar visible during transition to prevent flash
  if (sidebar) {
    sidebar.style.transform = 'translateX(0)';
  }
  
  // Load the calendar sidebar with the selected date
  fetch(`/admin/calendar_sidebar?date=${dateString}`)
    .then(response => response.text())
    .then(html => {
      sidebarContainer.innerHTML = html;
      sidebar = sidebarContainer.querySelector('[data-sidebar="sidebar"]').parentElement;
      sidebar.style.transform = 'translateX(0)';
      
      // Setup the sidebar again
      setupSidebar();
      
      openSidebar();
      sidebarLoaded = true;
      currentDate = dateString;
    })
    .catch(error => {
      // Silently handle error
    });
}

function toggleDentistsDropdown() {
  if (!dentistsButton) return;
  
  const isExpanded = dentistsButton.getAttribute('aria-expanded') === 'true';
  if (isExpanded) {
    closeDentistsDropdown();
  } else {
    openDentistsDropdown();
  }
}

function openDentistsDropdown() {
  if (!dentistsButton) {
    console.error('Dentists button not found');
    return;
  }
  
  // Close any other open dropdowns first
  closeDentistsDropdown();
  
  // Set button state
  dentistsButton.setAttribute('aria-expanded', 'true');
  dentistsButton.setAttribute('data-state', 'open');
  
  // Get or create dropdown menu
  let dropdownId = dentistsButton.getAttribute('aria-controls');
  if (!dropdownId) {
    dropdownId = 'dentists-dropdown';
    dentistsButton.setAttribute('aria-controls', dropdownId);
  }
  
  // Check if dropdown already exists
  let dropdown = document.getElementById(dropdownId);
  
  if (!dropdown) {
    // Create dropdown if it doesn't exist
    dropdown = document.createElement('div');
    dropdown.id = dropdownId;
    dropdown.className = 'absolute z-50 min-w-[12rem] overflow-hidden rounded-md border bg-white shadow-lg';
    dropdown.setAttribute('role', 'menu');
    dropdown.setAttribute('aria-orientation', 'vertical');
    dropdown.style.position = 'absolute';
    
    // Add dropdown items (you'll need to fetch these from your backend)
    dropdown.innerHTML = `
      <div class="py-1" role="none">
        <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">
          All Dentists
        </button>
        <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">
          Jennifer Lee
        </button>
        <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">
          Michael Brown
        </button>
      </div>
    `;
    
    document.body.appendChild(dropdown);
  }
  
  // Position the dropdown
  const rect = dentistsButton.getBoundingClientRect();
  dropdown.style.top = `${rect.bottom + window.scrollY}px`;
  dropdown.style.left = `${rect.left + window.scrollX}px`;
  dropdown.style.minWidth = `${rect.width}px`;
  
  // Show the dropdown
  dropdown.style.display = 'block';
  dropdown.style.opacity = '1';
  dropdown.style.transform = 'translateY(0)';
  dropdown.style.transition = 'opacity 0.1s ease-out, transform 0.1s ease-out';
  
  // Handle clicks on dropdown items
  dropdown.querySelectorAll('button').forEach(item => {
    item.addEventListener('click', (e) => {
      e.stopPropagation();
      const dentistName = e.target.textContent.trim();
      console.log('Selected dentist:', dentistName);
      // Handle dentist selection here
      closeDentistsDropdown();
    });
  });
  
  dentistsDropdown = dropdown;
}

function closeDentistsDropdown() {
  if (!dentistsButton) return;
  
  const dropdownId = dentistsButton.getAttribute('aria-controls');
  if (!dropdownId) return;
  
  const dropdown = document.getElementById(dropdownId);
  if (dropdown) {
    // Animate out
    dropdown.style.opacity = '0';
    dropdown.style.transform = 'translateY(-10px)';
    
    // Remove after animation
    setTimeout(() => {
      if (dropdown && dropdown.parentNode) {
        dropdown.style.display = 'none';
      }
    }, 100);
  }
  
  // Reset button state
  dentistsButton.setAttribute('aria-expanded', 'false');
  dentistsButton.setAttribute('data-state', 'closed');
}

function setupAppointmentDetailButtons() {
  // The buttons don't have data-appointment-detail attribute, so we need to target them differently
  // Looking for buttons with the specific aria-label "View appointment details"
  const appointmentButtons = sidebar.querySelectorAll('button[aria-label="View appointment details"]');
  if (!appointmentButtons.length) {
    return;
  }
  
  appointmentButtons.forEach(button => {
    // Remove any existing event listeners to prevent duplicates
    button.removeEventListener('click', handleAppointmentDetailClick);
    button.addEventListener('click', handleAppointmentDetailClick);
  });
}

function handleAppointmentDetailClick(event) {
  event.preventDefault();
  const bookingId = event.currentTarget.getAttribute('data-booking-id');
  loadInnerSidebar(bookingId);
}

function loadInnerSidebar(bookingId) {
  // Load the inner sidebar content via AJAX with booking ID
  let url = '/admin/inner_calendar_sidebar';
  if (bookingId) {
    url += `?booking_id=${bookingId}`;
  }
  
  fetch(url)
    .then(response => response.text())
    .then(html => {
      sidebarContainer.innerHTML = html;
      sidebar = sidebarContainer.querySelector('[data-sidebar="sidebar"]').parentElement;
      // Show the sidebar immediately
      sidebar.style.transform = 'translateX(0)';
      setupInnerSidebar();
    })
    .catch(error => {
      // Silently handle error
    });
}

function setupInnerSidebar() {
  if (!sidebar) {
    return;
  }
  
  // Create overlay if it doesn't exist
  if (!overlay) {
    createOverlay();
    overlay.style.display = 'block';
  }
  
  // Setup back button
  const backButton = sidebar.querySelector('[aria-label="Go back"]');
  if (backButton) {
    backButton.addEventListener('click', handleBackButtonClick);
  }
}

function handleBackButtonClick(event) {
  event.preventDefault();
  
  // Keep the sidebar visible during transition to prevent flash
  sidebar.style.transform = 'translateX(0)';
  
  // Load the main calendar sidebar directly
  fetch('/admin/calendar_sidebar')
    .then(response => response.text())
    .then(html => {
      sidebarContainer.innerHTML = html;
      sidebar = sidebarContainer.querySelector('[data-sidebar="sidebar"]').parentElement;
      sidebar.style.transform = 'translateX(0)';
      
      // Setup the main sidebar again
      setupSidebar();
      
      // Make sure appointment detail buttons are set up properly
      setupAppointmentDetailButtons();
      
      openSidebar();
      sidebarLoaded = true;
    })
    .catch(error => {
      // Silently handle error
    });
}

/**
 * Set up the new action button in the calendar sidebar
 */
function setupNewActionButton() {
  const newActionButton = sidebar.querySelector('[data-modal-target="calendarNewActionModal"]');
  if (!newActionButton) {
    return;
  }
  
  // The button already has the data-modal-target attribute which will be handled by the modal initializer
  // We just need to add any additional data attributes for the current date or selected appointment
  
  // Get the current date from the date button
  const dateButton = sidebar.querySelector('[data-action="navigate-date"][data-date]');
  if (dateButton) {
    const dateString = dateButton.dataset.date;
    newActionButton.dataset.modalDataDueDate = dateString;
  }
}

/**
 * Set up the appointment toggle functionality
 */
function setupAppointmentToggle() {
  // Make the toggle function globally available
  window.toggleAppointments = function(button) {
    // Toggle the button state
    const isExpanded = button.getAttribute('aria-expanded') === 'true';
    button.setAttribute('aria-expanded', !isExpanded);
    button.setAttribute('title', isExpanded ? 'Expand appointments' : 'Collapse appointments');
    button.setAttribute('aria-label', isExpanded ? 'Expand appointments' : 'Collapse appointments');
    
    // Find the sidebar container
    const sidebarContainer = document.querySelector('[data-sidebar="sidebar"]');
    if (sidebarContainer) {
      // Toggle the compact class on the sidebar container
      sidebarContainer.classList.toggle('compact-appointments-mode');
    }
  };
  
  // Also add a click event listener to the button for browsers that don't support onclick
  const toggleButton = document.querySelector('[data-appointment-toggle]');
  if (toggleButton) {
    toggleButton.addEventListener('click', function() {
      window.toggleAppointments(this);
    });
  }
}
