<div id="site-navbar" class="flex flex-col w-full <%= controller_path.start_with?("admin/calendar") ? 'sticky top-0' : 'relative' %> z-[1050]">
  <div class="flex items-center justify-between px-8 border-b border-gray-700/30" style="background-color: rgba(161, 161, 161, 1); backdrop-filter: blur(10px); box-shadow: rgba(0, 0, 0, 0.15) 0px 1px 3px; height: 56px;">
    <div class="flex items-center">
      <div class="flex items-center space-x-1.5 group relative">
        <div class="relative">
          <div class="h-8 w-8 rounded-full overflow-hidden flex-shrink-0 ring-2 ring-white/10">
            <%= render "layouts/shared/user_avatar", user: current_user, width: 32, height: 32 %>
          </div>
          <div class="absolute -bottom-2 -right-3 h-6 w-6 rounded-full flex items-center justify-center border border-white/10" style="background: rgba(80, 80, 80, 0.9); backdrop-filter: blur(8px);">
            <span class="text-[12px] font-semibold text-white/90"><%= current_user.initials %></span>
          </div>
        </div>
        <div class="absolute -bottom-8 left-0 bg-gray-900 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-10">
          <%= current_user.full_name %>
        </div>
      </div>
<div class="relative ml-8" id="practiceSelector">
  <% if current_user.practices.length > 1 %>
    <button id="practiceButton" class="flex items-center px-4 rounded-full focus:outline-none overflow-hidden relative" style="background: rgba(80, 80, 80, 0.9); backdrop-filter: blur(8px); border: 1px solid rgba(255, 255, 255, 0.08); height: 38px;">
      <div class="absolute inset-0 opacity-10" style="background-image: url(&quot;/sleek-dental-suite.png&quot;); background-size: cover; background-position: center center; mix-blend-mode: overlay;"></div>
      <div class="flex items-center relative z-10">
        <div class="h-6 w-8 flex-shrink-0 mr-2 relative">
          <div class="absolute inset-0 rounded-md" style="background: linear-gradient(135deg, rgb(74, 85, 104) 0%, rgb(45, 55, 72) 100%); border: 1px solid rgba(255, 255, 255, 0.2);"></div>
          <div class="absolute inset-0 flex items-center justify-center overflow-hidden rounded-[3px]">
            <% if Current.practice && Current.practice.logo.attached? %>
              <%= image_tag(Current.practice.logo, class: "h-full w-full object-cover") %>
            <% else %>
              <img alt="Practice photo" class="h-full w-full object-cover" src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/attachments/gen-images/public/sleek-dental-suite-TNPITZ5nSbuKE2qdyQst2MUsXVXNca.png">
            <% end %>
          </div>
        </div>
        <span class="text-white/90 text-sm font-medium mr-1.5">
          <% if Current.practice %>
            <%= Current.practice.name %>
          <% else %>
            All Practices
          <% end %>
        </span>
      </div>
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-4 w-4 text-white/70 relative z-10" style="transform: rotate(0deg); transition: transform 0.2s;">
        <path d="m6 9 6 6 6-6"></path>
      </svg>
    </button>
    <div id="practiceDropdown" class="absolute top-full left-0 mt-2 z-[9999] w-[350px] rounded-xl overflow-hidden" style="display: none; background: rgba(40, 40, 40, 0.95); backdrop-filter: blur(20px); border: 1px solid rgba(255, 255, 255, 0.1); box-shadow: rgba(0, 0, 0, 0.3) 0px 10px 25px -5px, rgba(0, 0, 0, 0.2) 0px 8px 10px -6px; animation: 0.2s ease-out 0s 1 normal forwards running fadeIn; transform-origin: center top;">
      <div class="px-4 py-3 border-b border-white/10">
        <h3 class="text-white/90 text-sm font-medium">Select Practice</h3>
      </div>
      <div class="px-4 py-3">
        <div class="relative">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-white/40">
            <circle cx="11" cy="11" r="8"></circle>
            <path d="m21 21-4.3-4.3"></path>
          </svg>
          <input placeholder="Search practices..." class="w-full bg-white/10 border border-transparent rounded-lg py-2 pl-9 pr-3 text-sm text-white/90 placeholder-white/40 focus:outline-none focus:ring-1 focus:ring-white/20 focus:bg-white/15 transition-all duration-200" type="text" value="">
        </div>
      </div>
      <div class="max-h-[280px] overflow-y-auto scrollbar-hide px-2 pb-2">
        <button id="showAllPracticesButton" class="flex items-center w-full px-3 py-2.5 text-sm rounded-lg transition-all duration-200 my-0.5 group text-white/80 hover:bg-white/10 hover:text-white">
          <div class="h-8 w-8 flex-shrink-0 mr-3 relative rounded-md overflow-hidden">
            <div class="absolute inset-0" style="background: linear-gradient(135deg, rgb(74, 85, 104) 0%, rgb(45, 55, 72) 100%); border: 1px solid rgba(255, 255, 255, 0.2);"></div>
            <div class="absolute inset-0 flex items-center justify-center overflow-hidden">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-layout-grid h-5 w-5 text-white/70">
                <rect width="7" height="7" x="3" y="3" rx="1"></rect>
                <rect width="7" height="7" x="14" y="3" rx="1"></rect>
                <rect width="7" height="7" x="14" y="14" rx="1"></rect>
                <rect width="7" height="7" x="3" y="14" rx="1"></rect>
              </svg>
            </div>
          </div>
          <div class="flex-1 flex flex-col items-start">
            <span class="font-medium">Show All Practices</span>
          </div>
        </button>

        <% current_user.practices.each do |practice| %>
          <% practice_active = Current.practice_id == practice.id %>
          <button class="flex items-center w-full px-3 py-2.5 text-sm rounded-lg transition-all duration-200 my-0.5 group <%= practice_active ? 'bg-white/15 text-white' : 'text-white/80 hover:bg-white/10 hover:text-white' %>" data-practice-id="<%= practice.id %>">
            <div class="h-8 w-8 flex-shrink-0 mr-3 relative rounded-md overflow-hidden">
              <div class="absolute inset-0" style="background: linear-gradient(135deg, rgb(74, 85, 104) 0%, rgb(45, 55, 72) 100%); border: 1px solid rgba(255, 255, 255, 0.2);"></div>
              <div class="absolute inset-0 flex items-center justify-center overflow-hidden">
                <% if practice.logo.attached? %>
              <%= image_tag(url_for(practice.logo), class: "h-full w-full object-cover") %>
                <% else %>
                  <img alt="Practice photo" class="h-full w-full object-cover" src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/attachments/gen-images/public/sleek-dental-suite-TNPITZ5nSbuKE2qdyQst2MUsXVXNca.png">
                <% end %>
              </div>
            </div>
            <div class="flex-1 flex flex-col items-start">
              <span class="font-medium"><%= practice.name %></span>
              <span class="text-xs text-white/50 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-map-pin h-3 w-3 mr-1 inline-block">
                  <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
                  <circle cx="12" cy="10" r="3"></circle>
                </svg>
                <%= practice.address_line_1 %>, <%= practice.postcode %>
              </span>
            </div>
            <% if practice_active %>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check h-4 w-4 text-white/70 ml-2">
                <path d="M20 6 9 17l-5-5"></path>
              </svg>
            <% else %>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check h-4 w-4 text-white/70 ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <path d="M20 6 9 17l-5-5"></path>
              </svg>
            <% end %>
          </button>
        <% end %>
      </div>
      <div class="px-4 py-3 border-t border-white/10 flex justify-center items-center">
        <span class="text-xs text-white/50"><%= current_user.practices.count %> practices</span>
      </div>
    </div>
  <% else %>
    <button id="practiceButton" class="flex items-center px-4 rounded-full focus:outline-none overflow-hidden relative" style="background: rgba(80, 80, 80, 0.9); backdrop-filter: blur(8px); border: 1px solid rgba(255, 255, 255, 0.08); height: 38px;">
      <div class="absolute inset-0 opacity-10" style="background-image: url(&quot;/sleek-dental-suite.png&quot;); background-size: cover; background-position: center center; mix-blend-mode: overlay;"></div>
      <div class="flex items-center relative z-10">
        <div class="h-6 w-8 flex-shrink-0 mr-2 relative">
          <div class="absolute inset-0 rounded-md" style="background: linear-gradient(135deg, rgb(74, 85, 104) 0%, rgb(45, 55, 72) 100%); border: 1px solid rgba(255, 255, 255, 0.2);"></div>
          <div class="absolute inset-0 flex items-center justify-center overflow-hidden rounded-[3px]">
            <% if Current.practice && Current.practice.logo.attached? %>
              <%= image_tag(Current.practice.logo, class: "h-full w-full object-cover") %>
            <% else %>
              <img alt="Practice photo" class="h-full w-full object-cover" src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/attachments/gen-images/public/sleek-dental-suite-TNPITZ5nSbuKE2qdyQst2MUsXVXNca.png">
            <% end %>
          </div>
        </div>
        <span class="text-white/90 text-sm font-medium mr-1.5">
          <% if current_user.practices.any? %>
            <%= current_user.practices.first.name %>
          <% else %>
            No Practice
          <% end %>
        </span>
      </div>
    </button>
  <% end %>
</div>
    </div>
    <div class="flex-1 flex items-center justify-center px-4 max-w-[60%]">
      <nav class="flex items-center space-x-5">
        <div class="relative">
          <a href="#" role="button" data-dropdown-toggle="patients-dropdown" class="flex items-center px-2 py-1 text-sm font-medium transition-all duration-200 whitespace-nowrap active:scale-95 focus:outline-none focus:ring-1 focus:ring-white/20 text-gray-300 hover:text-white border-b-2 border-transparent <%= request.path.start_with?('/admin/patients') ? 'text-white border-white/80' : '' %>">
            <span class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users h-3.5 w-3.5 mr-1.5 opacity-80">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
              </svg>
              Patients
            </span>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="dropdown-arrow lucide lucide-chevron-down ml-1 h-3 w-3 opacity-70 transition-transform duration-200" style="transform: rotate(0deg);">
              <path d="m6 9 6 6 6-6"></path>
            </svg>
          </a>
          <div id="patients-dropdown" class="hidden absolute top-full left-0 mt-1 py-1 bg-gray-800 rounded-md shadow-lg z-20 min-w-[180px]" style="background: rgba(80, 80, 80, 0.9); backdrop-filter: blur(8px); border: 1px solid rgba(255, 255, 255, 0.08); animation: 0.2s ease-out 0s 1 normal forwards running fadeIn;">
            <a href="/admin/patients" class="flex items-center w-full px-4 py-2 text-sm text-left text-white/90 hover:bg-white/10 hover:text-white transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-list h-4 w-4 mr-2 opacity-70">
                <line x1="8" x2="21" y1="6" y2="6"></line>
                <line x1="8" x2="21" y1="12" y2="12"></line>
                <line x1="8" x2="21" y1="18" y2="18"></line>
                <line x1="3" x2="3.01" y1="6" y2="6"></line>
                <line x1="3" x2="3.01" y1="12" y2="12"></line>
                <line x1="3" x2="3.01" y1="18" y2="18"></line>
              </svg>
              <span>Patient List</span>
            </a>
            <a href="/admin/crm" class="flex items-center w-full px-4 py-2 text-sm text-left text-white/90 hover:bg-white/10 hover:text-white transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-layout-grid h-4 w-4 mr-2 opacity-70">
                <rect width="7" height="7" x="3" y="3" rx="1"></rect>
                <rect width="7" height="7" x="14" y="3" rx="1"></rect>
                <rect width="7" height="7" x="14" y="14" rx="1"></rect>
                <rect width="7" height="7" x="3" y="14" rx="1"></rect>
              </svg>
              <span>Patient CRM</span>
            </a>
          </div>
        </div>
        <div class="relative">
          <a href="/admin/calendar_bookings/staff_calendar" class="flex items-center px-2 py-1 text-sm font-medium transition-all duration-200 whitespace-nowrap active:scale-95 focus:outline-none focus:ring-1 focus:ring-white/20 <%= request.path.start_with?('/admin/calendar_bookings') ? 'text-white border-b-2 border-white/80' : 'text-gray-300 hover:text-white border-b-2 border-transparent' %>">
            <span>Calendar</span>
          </a>
        </div>
        <div class="relative">
          <a href="/admin/treatment_plans" class="flex items-center px-2 py-1 text-sm font-medium transition-all duration-200 whitespace-nowrap active:scale-95 focus:outline-none focus:ring-1 focus:ring-white/20 <%= request.path.start_with?('/admin/treatment_plans') ? 'text-white border-b-2 border-white/80' : 'text-gray-300 hover:text-white border-b-2 border-transparent' %>">
            <span>Treatment Plans</span>
          </a>
        </div>
        <div class="relative">
          <a href="/admin/lab_works" class="flex items-center px-2 py-1 text-sm font-medium transition-all duration-200 whitespace-nowrap active:scale-95 focus:outline-none focus:ring-1 focus:ring-white/20 <%= request.path.start_with?('/admin/lab_works') ? 'text-white border-b-2 border-white/80' : 'text-gray-300 hover:text-white border-b-2 border-transparent' %>">
            <span>Lab-Work</span>
          </a>
        </div>
        <!-- Inventory Dropdown
        <div class="relative">
          <a href="#" role="button" data-dropdown-toggle="inventory-dropdown" class="flex items-center px-2 py-1 text-sm font-medium transition-all duration-200 whitespace-nowrap active:scale-95 focus:outline-none focus:ring-1 focus:ring-white/20 <%= request.path.start_with?('/admin/products') ? 'text-white border-b-2 border-white/80' : 'text-gray-300 hover:text-white border-b-2 border-transparent' %>">
            <span>Inventory</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="dropdown-arrow lucide lucide-chevron-down ml-1 h-3 w-3 opacity-70 transition-transform duration-200">
              <path d="m6 9 6 6 6-6"></path>
            </svg>
          </a>
          <div id="inventory-dropdown" class="hidden absolute top-full left-0 mt-1 py-1 bg-gray-800 rounded-md shadow-lg z-20 min-w-[180px]" style="background: rgba(80, 80, 80, 0.9); backdrop-filter: blur(8px); border: 1px solid rgba(255, 255, 255, 0.08); animation: 0.2s ease-out 0s 1 normal forwards running fadeIn;">
            <a href="/admin/products" class="flex items-center w-full px-4 py-2 text-sm text-left text-white/90 hover:bg-white/10 hover:text-white transition-colors">All products</a>
            <a href="/admin/products/new" class="flex items-center w-full px-4 py-2 text-sm text-left text-white/90 hover:bg-white/10 hover:text-white transition-colors">New product</a>
            <a href="/admin/product_categories" class="flex items-center w-full px-4 py-2 text-sm text-left text-white/90 hover:bg-white/10 hover:text-white transition-colors">Product categories</a>
            <a href="/admin/suppliers" class="flex items-center w-full px-4 py-2 text-sm text-left text-white/90 hover:bg-white/10 hover:text-white transition-colors">Suppliers</a>
            <a href="/admin/products/reserved" class="flex items-center w-full px-4 py-2 text-sm text-left text-white/90 hover:bg-white/10 hover:text-white transition-colors">Reserved items</a>
            <a href="/admin/products/low_stock" class="flex items-center w-full px-4 py-2 text-sm text-left text-white/90 hover:bg-white/10 hover:text-white transition-colors">Low stock items</a>
            <a href="/admin/products/stock_check" class="flex items-center w-full px-4 py-2 text-sm text-left text-white/90 hover:bg-white/10 hover:text-white transition-colors">Stock check</a>
            <a href="/admin/products/withdraw_add" class="flex items-center w-full px-4 py-2 text-sm text-left text-white/90 hover:bg-white/10 hover:text-white transition-colors">Inventory management</a>
            <a href="/admin/products/inventory_log" class="flex items-center w-full px-4 py-2 text-sm text-left text-white/90 hover:bg-white/10 hover:text-white transition-colors">Inventory log</a>
            <a href="/admin/products/purchase_orders" class="flex items-center w-full px-4 py-2 text-sm text-left text-white/90 hover:bg-white/10 hover:text-white transition-colors">Purchase orders</a>
          </div>
        </div>
        -->
        <div class="relative">
          <a href="/admin/hr_management/dashboard" class="flex items-center px-2 py-1 text-sm font-medium transition-all duration-200 whitespace-nowrap active:scale-95 focus:outline-none focus:ring-1 focus:ring-white/20 <%= request.path.start_with?('/admin/hr_management') ? 'text-white border-b-2 border-white/80' : 'text-gray-300 hover:text-white border-b-2 border-transparent' %>">
            <span>HR Management</span>
          </a>
        </div>
        <div class="relative">
          <a href="/admin/reports" class="flex items-center px-2 py-1 text-sm font-medium transition-all duration-200 whitespace-nowrap active:scale-95 focus:outline-none focus:ring-1 focus:ring-white/20 <%= request.path.start_with?('/admin/reports') ? 'text-white border-b-2 border-white/80' : 'text-gray-300 hover:text-white border-b-2 border-transparent' %>">
            <span>Reports</span>
          </a>
        </div>
        <div class="relative">
          <a href="/admin/signature_requests" class="flex items-center px-2 py-1 text-sm font-medium transition-all duration-200 active:scale-95 focus:outline-none focus:ring-1 focus:ring-white/20 <%= request.path.start_with?('/admin/secure_send') ? 'text-white border-b-2 border-white/80' : 'text-gray-300 hover:text-white' %>">
            <span>Secure Send</span>
          </a>
        </div>
      </nav>
    </div>
    <div class="flex items-center">
      <div class="flex items-center rounded-full px-3 py-0.5" style="background: rgba(80, 80, 80, 0.9); backdrop-filter: blur(8px); border: 1px solid rgba(255, 255, 255, 0.08);">
        <button id="searchButton" class="group relative p-1.5 px-2 mx-0.5 rounded-full transition-all duration-300 hover:bg-white/10" onclick="toggleSearchDropdown()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search h-5 w-5 text-white/80 group-hover:text-white transition-all duration-300 transform group-hover:scale-105">
            <circle cx="11" cy="11" r="8"></circle>
            <path d="m21 21-4.3-4.3"></path>
          </svg>
          <span class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[21px] h-[21px] rounded-full bg-white/0 group-hover:bg-white/5 transition-all duration-300"></span>
        </button>

        <div id="searchDropdown" class="absolute z-[9999] flex-col items-center rounded-xl overflow-hidden w-[420px] hidden" style="top: 56px; right: 8px; background-color: rgb(245, 245, 247); box-shadow: rgba(0, 0, 0, 0.15) 0px 8px 20px, rgba(0, 0, 0, 0.1) 0px 2px 4px;">
          <div class="w-full px-2.5 pt-2.5">
            <div class="relative w-full rounded-full overflow-hidden" style="background-color: rgba(230, 230, 235, 0.8); border: 1px solid rgba(0, 0, 0, 0.06); box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 2px inset;">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search absolute left-3.5 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.3-4.3"></path>
              </svg>
              <input placeholder="Search patients..." class="w-full py-2.5 pl-10 pr-3.5 text-gray-800 placeholder-gray-500 focus:outline-none text-sm font-medium bg-transparent" type="text" value="" id="searchInput" oninput="searchPatients(this.value)">
            </div>
          </div>
          <div class="w-full mt-2">
            <!-- Search results container -->
            <div class="max-h-[400px] overflow-y-auto w-full" id="searchResultsContainer">
              <!-- Search results will be loaded here -->
            </div>
          </div>
        </div>
        <button id="plus-button" class="group relative p-1.5 px-2 mx-0.5 rounded-full transition-all duration-300 hover:bg-white/10">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-5 w-5 text-white/80 group-hover:text-white transition-all duration-300 transform group-hover:scale-105">
            <path d="M5 12h14"></path>
            <path d="M12 5v14"></path>
          </svg>
          <span class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[21px] h-[21px] rounded-full bg-white/0 group-hover:bg-white/5 transition-all duration-300"></span>
        </button>

        <div id="plus-dropdown" class="hidden absolute z-[9999] mt-1 w-[250px] rounded-md overflow-hidden" style="top: 33px; right: 180px; background: rgba(80, 80, 80, 0.9); backdrop-filter: blur(8px); border: 1px solid rgba(255, 255, 255, 0.08); animation: 0.2s ease-out 0s 1 normal forwards running fadeIn; transform-origin: center top;">
          <div class="max-h-[400px] overflow-y-auto scrollbar-hide py-1">
            <%= form_tag create_temporary_admin_patients_path, method: :post, class: "w-full" do %>
              <button type="submit" class="flex items-center w-full px-4 py-2 text-sm text-left text-white/90 hover:bg-white/10 hover:text-white transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user-plus h-4 w-4 mr-2 opacity-70">
                  <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                  <circle cx="9" cy="7" r="4"></circle>
                  <line x1="19" x2="19" y1="8" y2="14"></line>
                  <line x1="22" x2="16" y1="11" y2="11"></line>
                </svg>
                <span>Add Patient</span>
              </button>
            <% end %>
            <a href="/admin/lab_works/new" class="flex items-center w-full px-4 py-2 text-sm text-left text-white/90 hover:bg-white/10 hover:text-white transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-flask-round h-4 w-4 mr-2 opacity-70">
                <path d="M10 2v7.31"></path>
                <path d="M14 9.3V2"></path>
                <path d="M8.5 2h7"></path>
                <path d="M14 9.3a6.5 6.5 0 1 1-4 0"></path>
              </svg>
              <span>Create New Lab-Work</span>
            </a>
            <a href="/admin/treatment_plans/new" class="flex items-center w-full px-4 py-2 text-sm text-left text-white/90 hover:bg-white/10 hover:text-white transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clipboard-list h-4 w-4 mr-2 opacity-70">
                <rect width="8" height="4" x="8" y="2" rx="1" ry="1"></rect>
                <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                <path d="M12 11h4"></path>
                <path d="M12 16h4"></path>
                <path d="M8 11h.01"></path>
                <path d="M8 16h.01"></path>
              </svg>
              <span>Create New Treatment Plan</span>
            </a>
            <a href="/admin/products/new" class="flex items-center w-full px-4 py-2 text-sm text-left text-white/90 hover:bg-white/10 hover:text-white transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-package h-4 w-4 mr-2 opacity-70">
                <path d="M16.5 9.4 7.55 4.24"></path>
                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                <polyline points="3.29 7 12 12 20.71 7"></polyline>
                <line x1="12" x2="12" y1="22" y2="12"></line>
              </svg>
              <span>Create New Product</span>
            </a>
            <a href="/admin/products/add" class="flex items-center w-full px-4 py-2 text-sm text-left text-white/90 hover:bg-white/10 hover:text-white transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-package-plus h-4 w-4 mr-2 opacity-70">
                <path d="M16.5 9.4 7.55 4.24"></path>
                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                <polyline points="3.29 7 12 12 20.71 7"></polyline>
                <line x1="12" x2="12" y1="22" y2="12"></line>
                <path d="M19 15v6"></path>
                <path d="M16 18h6"></path>
              </svg>
              <span>Create New Stock Addition</span>
            </a>
            <a href="/admin/products/withdraw" class="flex items-center w-full px-4 py-2 text-sm text-left text-white/90 hover:bg-white/10 hover:text-white transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-package-minus h-4 w-4 mr-2 opacity-70">
                <path d="M16.5 9.4 7.55 4.24"></path>
                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                <polyline points="3.29 7 12 12 20.71 7"></polyline>
                <line x1="12" x2="12" y1="22" y2="12"></line>
                <path d="M16 18h6"></path>
              </svg>
              <span>Create New Product Withdrawal</span>
            </a>
            <a href="/admin/products/return" class="flex items-center w-full px-4 py-2 text-sm text-left text-white/90 hover:bg-white/10 hover:text-white transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-package-open h-4 w-4 mr-2 opacity-70">
                <path d="M20.91 8.84 8.56 2.23a1.93 1.93 0 0 0-1.81 0L3.1 4.13a2.12 2.12 0 0 0-.05 3.69l12.22 6.93a2 2 0 0 0 1.94 0L21 12.51a2.12 2.12 0 0 0-.09-3.67Z"></path>
                <path d="m3.09 8.84 12.35-6.61a1.93 1.93 0 0 1 1.81 0l3.65 1.9a2.12 2.12 0 0 1 .1 3.69L8.73 14.75a2 2 0 0 1-1.94 0L3 12.51a2.12 2.12 0 0 1 .09-3.67Z"></path>
                <line x1="12" x2="12" y1="22" y2="13"></line>
                <path d="M20 13.5v3.37a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13.5"></path>
              </svg>
              <span>Create New Stock Return</span>
            </a>
            <a href="/admin/products/check_in" class="flex items-center w-full px-4 py-2 text-sm text-left text-white/90 hover:bg-white/10 hover:text-white transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clipboard-check h-4 w-4 mr-2 opacity-70">
                <rect width="8" height="4" x="8" y="2" rx="1" ry="1"></rect>
                <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                <path d="m9 14 2 2 4-4"></path>
              </svg>
              <span>Create New Stock Check-In</span>
            </a>
            <a href="/admin/products/purchase_orders/new" class="flex items-center w-full px-4 py-2 text-sm text-left text-white/90 hover:bg-white/10 hover:text-white transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shopping-cart h-4 w-4 mr-2 opacity-70">
                <circle cx="8" cy="21" r="1"></circle>
                <circle cx="19" cy="21" r="1"></circle>
                <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"></path>
              </svg>
              <span>Create New Purchase Order</span>
            </a>
          </div>
        </div>
        <button class="group relative p-1.5 px-2 mx-0.5 rounded-full transition-all duration-300 hover:bg-white/10">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-star h-5 w-5 text-white/80 group-hover:text-white transition-all duration-300 transform group-hover:scale-105">
            <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
          </svg>
          <span class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[21px] h-[21px] rounded-full bg-white/0 group-hover:bg-white/5 transition-all duration-300"></span>
        </button>
                <a href="/admin/general_settings" class="group relative p-1.5 px-2 mx-0.5 rounded-full transition-all duration-300 hover:bg-white/10">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-settings h-5 w-5 text-white/80 group-hover:text-white transition-all duration-300 transform group-hover:scale-105">
            <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
            <circle cx="12" cy="12" r="3"></circle>
          </svg>
          <span class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[21px] h-[21px] rounded-full bg-white/0 group-hover:bg-white/5 transition-all duration-300"></span>
        </a>
        <a href="/admin/conversations" class="group relative p-1.5 px-2 mx-0.5 rounded-full transition-all duration-300 hover:bg-white/10">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square h-5 w-5 text-white/80 group-hover:text-white transition-all duration-300 transform group-hover:scale-105">
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
          </svg>
          <span class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[21px] h-[21px] rounded-full bg-white/0 group-hover:bg-white/5 transition-all duration-300"></span>
        </a>
        <button id="actions-toggle" class="group relative p-1.5 px-2 mx-0.5 rounded-full transition-all duration-300 hover:bg-white/10">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-square h-5 w-5 text-white/80 group-hover:text-white transition-all duration-300 transform group-hover:scale-105">
            <polyline points="9 11 12 14 22 4"></polyline>
            <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
          </svg>
          <span class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[21px] h-[21px] rounded-full bg-white/0 group-hover:bg-white/5 transition-all duration-300"></span>
        </button>
        <button id="calendar-button" class="group relative p-1.5 px-2 mx-0.5 rounded-full transition-all duration-300 hover:bg-white/10">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-5 w-5 text-white/80 group-hover:text-white transition-all duration-300 transform group-hover:scale-105">
            <path d="M8 2v4"></path>
            <path d="M16 2v4"></path>
            <rect width="18" height="18" x="3" y="4" rx="2"></rect>
            <path d="M3 10h18"></path>
          </svg>
          <span class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[21px] h-[21px] rounded-full bg-white/0 group-hover:bg-white/5 transition-all duration-300"></span>
        </button>
        <button id="notification-toggle" class="group relative p-1.5 px-2 mx-0.5 rounded-full transition-all duration-300 hover:bg-white/10">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bell h-5 w-5 text-white/80 group-hover:text-white transition-all duration-300 transform group-hover:scale-105">
            <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path>
            <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path>
          </svg>
          <% if current_user.notifications.unread.count > 0 %>
            <span class="absolute -top-1 -right-1 h-5 w-5 bg-red-500 rounded-full flex items-center justify-center text-[10px] font-bold text-white" id="notification_count">
              <%= current_user.notifications.unread.count %>
            </span>
          <% end %>
          <span class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[21px] h-[21px] rounded-full bg-white/0 group-hover:bg-white/5 transition-all duration-300"></span>
        </button>

      </div>
    </div>
  </div>
  <style>
    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes fadeOut {
      from {
        opacity: 1;
        transform: translateY(0);
      }
      to {
        opacity: 0;
        transform: translateY(-10px);
      }
    }

    .fadeIn {
      animation: fadeIn 0.2s ease-out forwards;
    }

    .fadeOut {
      animation: fadeOut 0.2s ease-out forwards;
    }
  </style>

  <script>
    // Function to toggle search dropdown visibility
    function toggleSearchDropdown() {
      // console.log('DEBUG: toggleSearchDropdown called');
      const searchDropdown = document.getElementById('searchDropdown');
      // console.log('DEBUG: searchDropdown element:', searchDropdown);
      if (!searchDropdown) {
        console.error('DEBUG: searchDropdown element not found!');
        return;
      }

      // Check if dropdown is currently hidden
      const isHidden = searchDropdown.classList.contains('hidden');
      // console.log('DEBUG: isHidden:', isHidden);
      if (!isHidden) {
        // If visible, hide it
        // console.log('DEBUG: Hiding dropdown');
        searchDropdown.classList.add('hidden');
        searchDropdown.classList.remove('flex');
      } else {
        // If hidden, show it
        // console.log('DEBUG: Showing dropdown');
        // Clear search results container first to prevent flash of old content
        const searchResultsContainer = document.getElementById('searchResultsContainer');
        if (searchResultsContainer) {
          searchResultsContainer.innerHTML = '';
          // console.log('DEBUG: Cleared search results container');
        }

        // Show the dropdown
        searchDropdown.classList.remove('hidden');
        searchDropdown.classList.add('flex');
        // console.log('DEBUG: Classes after toggle:', searchDropdown.className);
        // Focus input after a short delay
        setTimeout(() => {
          const searchInput = document.getElementById('searchInput');
          // console.log('DEBUG: searchInput element:', searchInput);
          if (searchInput) {
            searchInput.focus();
            // console.log('DEBUG: Input focused');
            // Clear any previous value
            searchInput.value = '';
            // Load initial results (empty search)
            // console.log('DEBUG: Loading initial search results');
            searchPatients('');
          } else {
            console.error('DEBUG: searchInput element not found!');
          }
        }, 100);
      }
    }

    // Simple search functionality
    function searchPatients(query) {
      // console.log('DEBUG: searchPatients called with query:', query);
      // Use the specific ID we added to the search results container
      const searchResultsContainer = document.getElementById('searchResultsContainer');
      // console.log('DEBUG: searchResultsContainer element:', searchResultsContainer);
      if (!searchResultsContainer) {
        console.error('DEBUG: searchResultsContainer element not found!');
        return;
      }

      // Clear the container immediately to prevent flash of old content
      searchResultsContainer.innerHTML = '';

      // Only search if query is at least 3 characters or empty (for initial load)
      if (query.length >= 3 || query.length === 0) {
        // console.log('DEBUG: Query length valid, proceeding with search');
        // Show loading indicator
        searchResultsContainer.innerHTML = '<div class="px-3.5 py-3 text-center">Searching...</div>';
        // console.log('DEBUG: Loading indicator displayed');
        // Fetch search results
        const url = `/admin/patients/search?query=${encodeURIComponent(query)}`;
        // console.log('DEBUG: Fetching from URL:', url);
        fetch(url)
          .then(response => {
            // console.log('DEBUG: Fetch response status:', response.status);
            return response.text();
          })
          .then(html => {
            // console.log('DEBUG: Received HTML response, length:', html.length);
            // Update results container with response
            searchResultsContainer.innerHTML = html;
            // console.log('DEBUG: Search results loaded into container');
          })
          .catch(error => {
            console.error('DEBUG: Search error:', error);
            searchResultsContainer.innerHTML = '<div class="px-3.5 py-3">An error occurred while searching</div>';
          });
      } else {
        // console.log('DEBUG: Query too short, not searching');
      }
    }

    // Initialize search when page loads
    document.addEventListener('DOMContentLoaded', function() {
      // console.log('DEBUG: DOM loaded, initializing search');
      // Check if all required elements exist
      const searchButton = document.getElementById('searchButton');
      const searchDropdown = document.getElementById('searchDropdown');
      const searchInput = document.getElementById('searchInput');
      const searchResultsContainer = document.getElementById('searchResultsContainer');

      // console.log('DEBUG: Elements found:', {
      //   searchButton: searchButton ? 'Found ✓' : 'MISSING ✗',
      //   searchDropdown: searchDropdown ? 'Found ✓' : 'MISSING ✗',
      //   searchInput: searchInput ? 'Found ✓' : 'MISSING ✗',
      //   searchResultsContainer: searchResultsContainer ? 'Found ✓' : 'MISSING ✗'
      // });
      // Log dropdown style
      if (searchDropdown) {
        // console.log('DEBUG: Initial searchDropdown display:', window.getComputedStyle(searchDropdown).display);
        // console.log('DEBUG: searchDropdown inline style:', searchDropdown.style.display);
      }

      // Close search when clicking outside
      document.addEventListener('click', function(e) {
        const searchDropdown = document.getElementById('searchDropdown');
        const searchButton = document.getElementById('searchButton');

        // Only proceed if elements exist and dropdown is visible (doesn't have hidden class)
        if (searchDropdown && searchButton && !searchDropdown.classList.contains('hidden')) {
          // console.log('DEBUG: Detected click outside, checking targets');
          // Check if click is outside dropdown and button
          if (!searchDropdown.contains(e.target) && !searchButton.contains(e.target)) {
            // console.log('DEBUG: Click was outside, hiding dropdown');
            searchDropdown.classList.add('hidden');
            searchDropdown.classList.remove('flex');
          }
        }
      });

      // Prevent clicks inside dropdown from closing it
      document.getElementById('searchDropdown').addEventListener('click', function(e) {
        e.stopPropagation();
      });
    });
  </script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const button = document.getElementById('practiceButton');
      const dropdown = document.getElementById('practiceDropdown');

      // Toggle dropdown when button is clicked
      button.addEventListener('click', function(e) {
        e.stopPropagation();

        if (dropdown.style.display === 'none') {
          dropdown.style.display = 'block';
          button.querySelector('svg').style.transform = 'rotate(180deg)';
        } else {
          dropdown.style.display = 'none';
          button.querySelector('svg').style.transform = 'rotate(0deg)';
        }
      });

      // Close dropdown when clicking outside


      // Close dropdown when ESC key is pressed
      document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
          dropdown.style.display = 'none';
          button.querySelector('svg').style.transform = 'rotate(0deg)';
        }
      });
    });
  </script>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const plusButton = document.getElementById('plus-button');
      const plusDropdown = document.getElementById('plus-dropdown');

      // Toggle dropdown when button is clicked
      if (plusButton && plusDropdown) {
        plusButton.addEventListener('click', function(e) {
          e.stopPropagation();

          const isHidden = plusDropdown.classList.contains('hidden');

          if (isHidden) {
            plusDropdown.classList.remove('hidden');
            plusDropdown.classList.add('fadeIn');
          } else {
            plusDropdown.classList.add('hidden');
            plusDropdown.classList.remove('fadeIn');
          }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
          if (!plusDropdown.contains(e.target) && e.target !== plusButton) {
            plusDropdown.classList.add('hidden');
            plusDropdown.classList.remove('fadeIn');
          }
        });

        // Close dropdown when ESC key is pressed
        document.addEventListener('keydown', function(e) {
          if (e.key === 'Escape') {
            plusDropdown.classList.add('hidden');
            plusDropdown.classList.remove('fadeIn');
          }
        });
      }
    });
  </script>
</div>
